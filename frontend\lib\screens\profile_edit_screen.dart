import 'dart:typed_data';
import 'dart:io' as io show File;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/services/user_service.dart';
import 'package:wiggyz_app/widgets/shared/standard_form_components.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/widgets/shared/loading_error_states.dart';
import 'package:wiggyz_app/models/profile.dart';

class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final ImagePicker _picker = ImagePicker();
  final _formKey = GlobalKey<FormState>();
  late UserService _userService;
  bool _isLoading = false;
  XFile? _selectedImage;
  Uint8List? _selectedImageBytes;

  // Form controllers - Personal Information
  late TextEditingController _usernameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _bioController;
  late TextEditingController _locationController;
  late TextEditingController _dateOfBirthController;

  // Form controllers - Free Fire
  late TextEditingController _ffUsernameController;
  late TextEditingController _ffUidController;
  late TextEditingController _ffLevelController;
  late TextEditingController _ffServerController;
  String? _ffRank;
  String? _ffPreferredMode;

  // Form controllers - PUBG
  late TextEditingController _pubgUsernameController;
  late TextEditingController _pubgUidController;
  late TextEditingController _pubgLevelController;
  late TextEditingController _pubgServerController;
  String? _pubgRank;
  String? _pubgPreferredMode;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeForm();
    });
  }

  @override
  void dispose() {
    // Clean up controllers - Personal Information
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _bioController.dispose();
    _locationController.dispose();
    _dateOfBirthController.dispose();

    // Clean up controllers - Free Fire
    _ffUsernameController.dispose();
    _ffUidController.dispose();
    _ffLevelController.dispose();
    _ffServerController.dispose();

    // Clean up controllers - PUBG
    _pubgUsernameController.dispose();
    _pubgUidController.dispose();
    _pubgLevelController.dispose();
    _pubgServerController.dispose();

    super.dispose();
  }

  Future<void> _initializeForm() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    _userService = UserService(userProvider, authProvider);

    // Initialize controllers with current profile data - Personal Information
    _usernameController = TextEditingController(text: userProvider.username);
    _emailController = TextEditingController(text: userProvider.email);
    _phoneController = TextEditingController(
      text: userProvider.profile?.phone ?? '',
    );
    _bioController = TextEditingController(text: userProvider.bio);
    _locationController = TextEditingController(text: userProvider.location);

    // Initialize controllers with current profile data - Free Fire
    final ffProfile = userProvider.profile?.freeFireProfile;
    _ffUsernameController = TextEditingController(
      text: ffProfile?.username ?? '',
    );
    _ffUidController = TextEditingController(text: ffProfile?.uid ?? '');
    _ffLevelController = TextEditingController(
      text: ffProfile?.level?.toString() ?? '',
    );
    _ffServerController = TextEditingController(text: ffProfile?.server ?? '');
    _ffRank = ffProfile?.rank;
    _ffPreferredMode = ffProfile?.preferredMode;

    // Initialize controllers with current profile data - PUBG
    final pubgProfile = userProvider.profile?.pubgProfile;
    _pubgUsernameController = TextEditingController(
      text: pubgProfile?.username ?? '',
    );
    _pubgUidController = TextEditingController(text: pubgProfile?.uid ?? '');
    _pubgLevelController = TextEditingController(
      text: pubgProfile?.level?.toString() ?? '',
    );
    _pubgServerController = TextEditingController(
      text: pubgProfile?.server ?? '',
    );
    _pubgRank = pubgProfile?.rank;
    _pubgPreferredMode = pubgProfile?.preferredMode;

    setState(() {});
  }

  Future<void> _getImage(ImageSource source) async {
    try {
      final pickedFile = await _picker.pickImage(source: source);
      if (pickedFile != null) {
        setState(() {
          _selectedImage = pickedFile;
        });

        // For web compatibility, read bytes
        if (kIsWeb) {
          final bytes = await pickedFile.readAsBytes();
          setState(() {
            _selectedImageBytes = bytes;
          });
        }
      }
    } catch (e) {
      debugPrint('Image picker error: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error selecting image: $e')));
      }
    }
  }

  void _showImageSourceOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Change Profile Picture',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(
                    context,
                  ).primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.photo_camera,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                title: Text('Take Photo', style: GoogleFonts.poppins()),
                onTap: () {
                  Navigator.of(context).pop();
                  _getImage(ImageSource.camera);
                },
              ),
              const SizedBox(height: 10),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(
                    context,
                  ).primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.photo_library,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                title: Text(
                  'Choose from Gallery',
                  style: GoogleFonts.poppins(),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _getImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Upload image if selected
      if (_selectedImage != null) {
        try {
          if (!kIsWeb) {
            // For mobile platforms, convert XFile to File
            final file = io.File(_selectedImage!.path);
            await _userService.uploadProfileImage(file);
          } else {
            // For web, skip image upload for now
            // The image will still be displayed in the UI via _buildProfileImageWidget
            debugPrint('Web profile image upload not yet implemented');
            // TODO: Implement web-compatible image upload in future
          }
        } catch (e) {
          debugPrint('Error uploading profile image: $e');
          // Continue with profile update even if image upload fails
        }
      }

      // Update profile info with all fields
      final success = await _userService.updateUserProfile(
        username: _usernameController.text,
        email: _emailController.text,
        bio: _bioController.text,
        location: _locationController.text,
        phone: _phoneController.text,
        // Free Fire fields
        ffName: _ffUsernameController.text,
        ffUid: _ffUidController.text,
        ffLevel: int.tryParse(_ffLevelController.text),
        ffServer: _ffServerController.text,
        ffRank: _ffRank,
        ffPreferredMode: _ffPreferredMode,
        // PUBG fields
        pubgName: _pubgUsernameController.text,
        pubgUid: _pubgUidController.text,
        pubgLevel: int.tryParse(_pubgLevelController.text),
        pubgServer: _pubgServerController.text,
        pubgRank: _pubgRank,
        pubgPreferredMode: _pubgPreferredMode,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Profile updated successfully')));
        Navigator.of(context).pop(true); // Return true to indicate success
      } else if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to update profile')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error updating profile: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);

    Widget profileImage; // Show selected image if available
    if (_selectedImage != null) {
      profileImage = _buildProfileImageWidget();
    }
    // Or show existing profile image if available
    else if (userProvider.profileImageFile != null) {
      // For existing profile images, we need to handle them differently
      // This might need updating based on how UserProvider handles images
      profileImage = Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(50),
        ),
        child: const Icon(Icons.person, size: 50, color: Colors.grey),
      );
    }
    // Or show network image if available
    else if (userProvider.profileImageUrl.isNotEmpty) {
      profileImage = CachedNetworkImage(
        imageUrl: userProvider.profileImageUrl,
        width: 100,
        height: 100,
        fit: BoxFit.cover,
        placeholder: (context, url) => const CircularProgressIndicator(),
        errorWidget: (context, url, error) => const Icon(Icons.error),
      );
    }
    // Or show initials in a circle
    else {
      final initials =
          userProvider.username.isNotEmpty
              ? userProvider.username.substring(0, 1).toUpperCase()
              : '?';

      profileImage = Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            initials,
            style: GoogleFonts.poppins(
              fontSize: 40,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Edit Profile',
          style: GoogleFonts.poppins(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: Text(
              'Save',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Profile image
                      Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(50),
                            child: profileImage,
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: GestureDetector(
                              onTap: _showImageSourceOptions,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.camera_alt,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 30),

                      // Personal Information Section
                      _buildSectionHeader('Personal Information'),
                      const SizedBox(height: 15),

                      // Username field
                      _buildTextField(
                        controller: _usernameController,
                        label: 'Username',
                        hint: 'Enter your username',
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Username is required';
                          }
                          if (value.length < 3) {
                            return 'Username must be at least 3 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Email field
                      _buildTextField(
                        controller: _emailController,
                        label: 'Email',
                        hint: 'Enter your email',
                        icon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Email is required';
                          }
                          final emailRegex = RegExp(
                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                          );
                          if (!emailRegex.hasMatch(value)) {
                            return 'Enter a valid email address';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Bio field
                      _buildTextField(
                        controller: _bioController,
                        label: 'Bio',
                        hint: 'Tell us about yourself',
                        icon: Icons.description,
                        maxLines: 3,
                      ),
                      const SizedBox(height: 20),

                      // Location field
                      _buildTextField(
                        controller: _locationController,
                        label: 'Location',
                        hint: 'Where are you from?',
                        icon: Icons.location_on,
                      ),
                      const SizedBox(height: 20),

                      // Date of Birth field
                      _buildTextField(
                        controller: _dateOfBirthController,
                        label: 'Date of Birth',
                        hint: 'YYYY-MM-DD',
                        icon: Icons.calendar_today,
                      ),
                      const SizedBox(height: 30),

                      // Free Fire Profile Section
                      _buildSectionHeader('Free Fire Profile'),
                      const SizedBox(height: 15),

                      _buildTextField(
                        controller: _ffUsernameController,
                        label: 'Free Fire Username',
                        hint: 'Enter your Free Fire username',
                        icon: Icons.person,
                      ),
                      const SizedBox(height: 20),

                      _buildTextField(
                        controller: _ffUidController,
                        label: 'Free Fire UID',
                        hint: 'Enter your Free Fire UID',
                        icon: Icons.numbers,
                      ),
                      const SizedBox(height: 20),

                      _buildTextField(
                        controller: _ffLevelController,
                        label: 'Free Fire Level',
                        hint: 'Enter your level (1-100)',
                        icon: Icons.trending_up,
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 20),

                      _buildTextField(
                        controller: _ffServerController,
                        label: 'Free Fire Server',
                        hint: 'Enter your server region',
                        icon: Icons.public,
                      ),
                      const SizedBox(height: 20),

                      _buildDropdownField(
                        label: 'Free Fire Rank',
                        hint: 'Select your rank',
                        icon: Icons.military_tech,
                        value: _ffRank,
                        items: const [
                          'Bronze',
                          'Silver',
                          'Gold',
                          'Platinum',
                          'Diamond',
                          'Master',
                          'Grandmaster',
                          'Heroic',
                        ],
                        onChanged: (value) => setState(() => _ffRank = value),
                      ),
                      const SizedBox(height: 20),

                      _buildDropdownField(
                        label: 'Free Fire Preferred Mode',
                        hint: 'Select your preferred mode',
                        icon: Icons.gamepad,
                        value: _ffPreferredMode,
                        items: const [
                          'Solo',
                          'Duo',
                          'Squad',
                          'Ranked',
                          'Casual',
                        ],
                        onChanged:
                            (value) => setState(() => _ffPreferredMode = value),
                      ),
                      const SizedBox(height: 30),

                      // PUBG Profile Section
                      _buildSectionHeader('PUBG Profile'),
                      const SizedBox(height: 15),

                      _buildTextField(
                        controller: _pubgUsernameController,
                        label: 'PUBG Username',
                        hint: 'Enter your PUBG username',
                        icon: Icons.person,
                      ),
                      const SizedBox(height: 20),

                      _buildTextField(
                        controller: _pubgUidController,
                        label: 'PUBG UID',
                        hint: 'Enter your PUBG UID',
                        icon: Icons.numbers,
                      ),
                      const SizedBox(height: 20),

                      _buildTextField(
                        controller: _pubgLevelController,
                        label: 'PUBG Level',
                        hint: 'Enter your level (1-100)',
                        icon: Icons.trending_up,
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 20),

                      _buildTextField(
                        controller: _pubgServerController,
                        label: 'PUBG Server',
                        hint: 'Enter your server region',
                        icon: Icons.public,
                      ),
                      const SizedBox(height: 20),

                      _buildDropdownField(
                        label: 'PUBG Rank',
                        hint: 'Select your rank',
                        icon: Icons.military_tech,
                        value: _pubgRank,
                        items: const [
                          'Bronze',
                          'Silver',
                          'Gold',
                          'Platinum',
                          'Diamond',
                          'Crown',
                          'Ace',
                          'Conqueror',
                        ],
                        onChanged: (value) => setState(() => _pubgRank = value),
                      ),
                      const SizedBox(height: 20),

                      _buildDropdownField(
                        label: 'PUBG Preferred Mode',
                        hint: 'Select your preferred mode',
                        icon: Icons.gamepad,
                        value: _pubgPreferredMode,
                        items: const [
                          'Solo',
                          'Duo',
                          'Squad',
                          'Ranked',
                          'Classic',
                          'Arena',
                        ],
                        onChanged:
                            (value) =>
                                setState(() => _pubgPreferredMode = value),
                      ),
                      const SizedBox(height: 30),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required IconData icon,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 14,
            ),
          ),
          items:
              items.map((String item) {
                return DropdownMenuItem<String>(value: item, child: Text(item));
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildProfileImageWidget() {
    if (_selectedImage == null) return const SizedBox.shrink();

    if (kIsWeb && _selectedImageBytes != null) {
      return Image.memory(
        _selectedImageBytes!,
        width: 100,
        height: 100,
        fit: BoxFit.cover,
      );
    } else {
      // For mobile platforms, use future builder to read bytes
      return FutureBuilder<Uint8List>(
        future: _selectedImage!.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Image.memory(
              snapshot.data!,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
            );
          }
          return const SizedBox(
            width: 100,
            height: 100,
            child: Center(child: CircularProgressIndicator()),
          );
        },
      );
    }
  }
}
